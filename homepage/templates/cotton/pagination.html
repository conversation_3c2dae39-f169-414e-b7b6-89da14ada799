<!-- PAGINATION-->
<!-- For this to work, you need a -->
<!-- div with id="pagination-jump-point"-->
<!-- on the page where you use <c-pagination /> -->
<!-- so the pagination can jump to it -->
<!-- instead of reloading the page at the top -->

<nav class="w-full flex justify-center pb-16" aria-label="Page navigation">
    <ul class="inline-flex -space-x-px shadow-sm">

        <!-- FIRST / PREVIOS -->
        {% if page_obj.has_previous %}
            <li>
                <a href="?page=1#pagination-jump-point"
                   class="size-12 flex items-center justify-center ms-0 leading-tight font-bold bg-white border border-e-0 border-gray-300 rounded-s-lg hover:bg-gray-100 hover:text-gray-700">
                    <c-icons.chevron-double-left />
                </a>
            </li>
            <li>
                <a href="?page={{ page_obj.previous_page_number }}#pagination-jump-point"
                   class="size-12 flex items-center justify-center ms-0 leading-tight font-bold bg-white border border-e-0 border-gray-300 hover:bg-gray-100 hover:text-gray-700">
                    <c-icons.chevron-left />
                </a>
            </li>
        {% else %}
            <li>
                <div class="size-12 flex items-center justify-center ms-0 leading-tight text-gray-300 bg-white border border-e-0 border-gray-300 rounded-s-lg hover:bg-gray-100 hover:text-gray-700">
                    <c-icons.chevron-double-left />
                </div>
            </li>
            <li>
                <div class="size-12 flex items-center justify-center ms-0 leading-tight text-gray-300 bg-white border border-e-0 border-gray-300 hover:bg-gray-100 hover:text-gray-700">
                    <c-icons.chevron-left />
                </div>
            </li>
        {% endif %}

        <!-- NUMBERS -->
        <div id="pagination-numbers-list"
             class="max-w-36 flex scroll-smooth snap-x snap-mandatory overflow-auto scrollbar-hide">
            {% for num in range_list %}
                {% if num == page_obj.number %}
                    <li>
                        <div class="snap-start size-12 flex items-center justify-center leading-tight font-bold bg-white border border-e-0 border-gray-300 hover:bg-gray-100 hover:text-gray-700">
                            <p class="text-xl translate-y-[1px]">{{ num }}</p>
                        </div>
                    </li>
                {% else %}
                    <li>
                        <a href="?page={{ num }}#pagination-jump-point"
                           class="snap-start size-12 flex items-center justify-center leading-tight text-gray-500 bg-white border border-e-0 border-gray-300 hover:bg-gray-100 hover:text-gray-700">
                            <p class="text-xl translate-y-[1px]">{{ num }}</p>
                        </a>
                    </li>
                {% endif %}
            {% endfor %}
        </div>

        <!-- NEXT / LAST -->
        {% if page_obj.has_next %}
            <li>
                <a href="?page={{ page_obj.next_page_number }}#pagination-jump-point"
                   class="size-12 flex items-center justify-center leading-tight font-bold bg-white border border-gray-300 hover:bg-gray-100 hover:text-gray-700">
                    <c-icons.chevron-right />
                </a>
            </li>

            <li>
                <a href="?page={{ page_obj.paginator.num_pages }}#pagination-jump-point"
                   class="size-12 flex items-center justify-center leading-tight font-bold bg-white border border-gray-300 rounded-e-lg hover:bg-gray-100 hover:text-gray-700">
                    <c-icons.chevron-double-right />
                </a>
            </li>
        {% else %}
            <li>
                <div class="size-12 flex items-center justify-center leading-tight text-gray-300 bg-white border border-gray-300 hover:bg-gray-100 hover:text-gray-700">
                    <c-icons.chevron-right />
                </div>
            </li>

            <li>
                <div class="size-12 flex items-center justify-center leading-tight text-gray-300 bg-white border border-gray-300 rounded-e-lg hover:bg-gray-100 hover:text-gray-700">
                    <c-icons.chevron-double-right />
                </div>
            </li>
        {% endif %}
    </ul>
</nav>

<script>
    const paginationNumbersList = document.getElementById('pagination-numbers-list');
    const scrollWidth = document.querySelector("#pagination-numbers-list li").offsetWidth;

    const urlParams = new URLSearchParams(window.location.search);
    const page = urlParams.get('page');
    const currentPage = page ? parseInt(page) : 1;

    if (currentPage - 2 > 0) {
        paginationNumbersList.scrollLeft += scrollWidth * (currentPage - 2);

    }
</script>
