{% load custom_filters %}

<!-- Card Image -->
<div class="w-full max-w-[30rem] rounded-md bg-white shadow-md flex flex-col justify-self-center">
  <a href="{% if active_properties == False %}{% url 'single_inactive_property' property.id %}{% else %}{% url 'single_active_property' property.id %}{% endif %}"
     class="group">
    <div class="image-container relative h-80 overflow-hidden rounded-t-md">
      <img class="size-full object-cover group-hover:scale-105 transition duration-300"
           src="{{ property.main_image.url }}"
           alt="" />
      <div class="service-type absolute top-4 left-4 bg-green-600 text-gray-100 px-4 py-2 rounded-md">
        {{ property.service_type }}
      </div>
      <div class="property-type absolute bottom-0 left-4 bg-white px-4 py-2 rounded-t-md text-green-600">
        {{ property.property_type }}
      </div>
    </div>
  </a>

  <!-- Card Middle Section -->
  <div class="second-inset px-4 pt-8">
    <div class="flex justify-between">
      <h5 class="price text-xl font-bold mb-4">Оферта - {{ property.offer_nr }}</h5>
      <h5 class="price text-xl font-bold text-green-600 mb-4">{{ property.price|format_price }} {{ property.currency }}</h5>
    </div>
    <a class="title text-xl font-bold min-h-28 inline-block mb-8"
       href="{% if active_properties == False %}{% url 'single_inactive_property' property.id %}{% else %}{% url 'single_active_property' property.id %}{% endif %}">{{ property.heading }}</a>
    <p class="address flex items-center gap-2 mb-4">
      <c-icons.geo-pin class="size-8 text-green-600" />
      {{ property.location }}, {{ property.district }}
    </p>
  </div>

  <!-- Card Footer -->
  <div class="grid grid-cols-3 border-t-[1px] border-dashed border-green-600">
    <div class="footer-info-box flex flex-col items-center p-4 border-r-[1px] border-dashed border-green-600">
      <c-icons.ruler class="size-10 text-green-600" />
      <p>
        {{ property.area }} m<sup>2</sup>
      </p>
    </div>
    <div class="footer-info-box flex flex-col items-center p-4 border-r-[1px] border-dashed border-green-600">
      <c-icons.stairs class="size-10 text-green-600" />
      <abbr class="no-underline"
            title="Етаж {{ property.floor }} от {{ property.floor_count }}">{{ property.floor }} от {{ property.floor_count }}</abbr>
    </div>
    <div class="footer-info-box flex flex-col items-center p-4">
      <c-icons.heater class="size-10 text-green-600" />
      <p>{{ property.heating }}</p>
    </div>
  </div>
</div>
