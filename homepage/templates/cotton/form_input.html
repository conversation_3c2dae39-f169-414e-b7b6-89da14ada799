{% load widget_tweaks %}

<div class="w-full">
  {% for error in field.errors %}
    <p class="text-red-400 text-xl">{{ error }}</p>
  {% endfor %}
  <div class="flex gap-1">
    <label class="block text-gray-700 text-xl">
      {{ field.label }}
      {% if field.field.required is True %}
        <span class="text-red-400">*</span>
      {% endif %}
    </label>
  </div>

  {% if field.errors %}
    {% render_field field class='mb-4 block w-full px-3 py-2 border border-red-300 rounded-md shadow-sm focus:outline-none focus:ring-red-300 focus:border-red-300' %}
  {% else %}
    {% render_field field class='mb-4 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-anri-color focus:border-anri-color' %}
  {% endif %}
</div>
