{% load static %}

<!-- AGENT CARD -->
<div class="card p-4 relative flex flex-col items-center">
  {% if user.is_authenticated %}
    <div class="absolute z-20 top-1 right-1 group">
      <div class="relative">
        <a href="{% url 'edit_agent' agent.id %}"
           class="p-4 flex rounded-full bg-red-400 text-slate-50">
          <c-icons.pencil class="size-6" />
        </a>
        <span class="hidden group-hover:inline-block absolute top-14 right-0 mb-2 w-max bg-anri-color text-slate-100 text-xs rounded py-1 px-2">
          Редакция на брокер
        </span>
      </div>
    </div>
  {% endif %}

  <a href="{% url 'agent_detail' agent.id %}"
     class="absolute top-0 left-0 size-full"></a>
  {% if agent.image %}
    <!-- AGENT IMAGE -->
    <img src="{{ agent.image.url }}"
         alt=""
         class="w-40 h-48 object-cover mb-8 rounded-md" />
  {% else %}
    <!-- DEFAULT IMAGE -->
    <div class="mb-8">
      <c-icons.default-user />
    </div>
  {% endif %}

  <div class="flex flex-col text-xl gap-2">
    <!-- AGENT NAME -->
    <div class="flex justify-center gap-2">
      <p class="text-2xl">{{ agent.first_name }}</p>
      <p class="text-2xl">{{ agent.last_name }}</p>
    </div>

    <!-- AGENT PHONE -->
    <div class="z-20">
      <a href="tel:{{ agent.phone }}" class="flex gap-4 items-center">
        <c-icons.telephone class="size-6 text-accent" />
        <div class="underline underline-offset-4 decoration-solid flex gap-4">{{ agent.phone }}</div>
        <c-icons.box-arrow-up-right class="size-4" />
      </a>
    </div>

    <!-- AGENT EMAIL -->
    <div class="z-20">
      <a href="mailto:{{ agent.email }}" class="flex gap-4 items-center">
        <c-icons.envelope class="size-6 text-accent" />
        <div class="underline underline-offset-4 decoration-solid flex gap-4">{{ agent.email }}</div>
        <c-icons.box-arrow-up-right class="size-4" />
      </a>
    </div>
  </div>
</div>
