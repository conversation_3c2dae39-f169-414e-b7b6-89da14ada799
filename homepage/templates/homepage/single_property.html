{% extends "homepage/base.html" %}
{% load static %}
{% load custom_filters %}

{% block scripts %}
  <script src="{% static 'js/singleProperty.js' %}"></script>
{% endblock scripts %}

{% block content %}
  <section class="p-4 md:flex md:flex-col md:items-center relative">
    <!-- EDIT BUTTON -->
    {% if user.is_authenticated %}
      <div class="inline-block absolute group top-4 right-4">
        <div class="relative">
          <a href="{% url 'edit_property' property.id %}"
             class="p-4 flex rounded-full bg-red-400 text-slate-50">
            <c-icons.pencil class="size-6" />
          </a>
          <span class="hidden group-hover:inline-block absolute top-14 right-0 mb-2 w-max bg-anri-color text-slate-100 text-xs rounded py-1 px-2">
            Редакция на оферта
          </span>
        </div>
      </div>
    {% endif %}

    <!-- BIG Heading for Big Screens -->
    <h1 class="hidden md:block mb-12 text-4xl leading-none font-bold">{{ property.heading }}</h1>

    <!-- 2x2 GRID -->
    <div class=" max-w-4xl md:grid md:grid-cols-2 md:gap-16">
      <!-- CAROUSEL WITH BUTTONS -->
      <div class="mb-8 md:mb-0 flex flex-col items-center max-w-md">
        <div id="item-list"
             class="aspect-[3/4] item-list flex scroll-smooth snap-x snap-mandatory overflow-auto scrollbar-width scrollbar-hide">
          <!-- Main image comes first -->
          <img class="snap-start object-cover" src="{{ property.main_image.url }}" />

          {% for photo in photos %}<img class="snap-start object-cover" src="{{ photo.image.url }}" />{% endfor %}
        </div>

        <!-- Left/Right Buttons -->
        <div class="w-full mt-4 flex justify-between items-center bg-gray-100 p-2 rounded-full">
          <c-button-default id="prev-btn" class="bg-anri-color text-white p-2 size-12 rounded-full">
          <c-icons.arrow-left />
          </c-button-default>

          <!-- Numbers -->
          <p id="image-digits"
             class="tracking-[4px] text-xl font-bold text-anri-color"></p>

          <c-button-default id="next-btn" class="bg-anri-color text-white p-2 size-12 rounded-full">
          <c-icons.arrow-right />
          </c-button-default>
        </div>
      </div>

      <!-- PROPERTY DETAILS -->
      <div>
        <h1 class="text-2xl leading-none font-bold">{{ property.heading }}</h1>

        <div class="mb-12 ">
          <div class="flex flex-col self-start gap-0 text-xl">
            <p class="mb-4">Оферта №{{ property.offer_nr }}</p>
            <p class="flex items-center gap-4">
              <c-icons.geo-pin class="size-6" />
              {{ property.location }}
            </p>
            <p class="flex items-center gap-4">
              <c-icons.price-tag class="size-6" />
              {{ property.price|format_price }} {{ property.currency }}
            </p>

            <p class="flex items-center gap-4">
              <c-icons.ruler class="size-6" />
              <span>{{ property.area }} m<sup>2</sup></span>
            </p>

            <p class="flex items-center gap-4">
              <c-icons.stairs class="size-6" />
              <abbr class="no-underline"
                    title="Етаж {{ property.floor }} от {{ property.floor_count }}">{{ property.floor }} от {{ property.floor_count }}</abbr>
            </p>

            <p class="flex items-center gap-4">
              <c-icons.heater class="size-6" />
              {{ property.heating }}
            </p>

            <p class="flex items-center gap-4">
              <c-icons.wall class="size-6" />
              {{ property.structure }}
            </p>
          </div>
        </div>
      </div>

      <!-- DESCRIPTION -->
      <div>
        {% if property.description %}<p class="mb-8 text-xl">{{ property.description }}</p>{% endif %}

        {% if property.location_description %}
          <div class="mb-8">
            <p class="text-2xl font-bold">Местоположение</p>
            <p class="text-xl">{{ property.location_description }}</p>
          </div>
        {% endif %}

        {% if property.room_distribution %}
          <div class="mb-8">
            <p class="text-2xl font-bold">Разпределение</p>
            <p class="text-xl">{{ property.room_distribution }}</p>
          </div>
        {% endif %}

        {% if property.condition %}
          <div class="mb-8">
            <p class="text-2xl font-bold">Състояние</p>
            <p class="text-xl">{{ property.condition }}</p>
          </div>
        {% endif %}

        {% if property.building_description %}
          <div class="mb-8">
            <p class="text-2xl font-bold">Изложение</p>
            <p class="text-xl">{{ property.building_description }}</p>
          </div>
        {% endif %}

        {% if property.additional_information %}
          <div class="mb-8">
            <p class="text-2xl font-bold">Допълнителна Информация</p>
            <p class="text-xl">{{ property.additional_information }}</p>
          </div>
        {% endif %}
      </div>

      <!-- AGENT -->
      <div>
        {% if property.agent %}
          <h2 class="text-3xl font-bold">Брокер</h2>

          <div class="max-w-80 bg-white">
            <!-- AGENT CARD -->
            <c-agent-card :agent="property.agent">
            </c-agent-card>
          </div>
        {% endif %}
      </div>
    </div>
  </section>
{% endblock content %}
