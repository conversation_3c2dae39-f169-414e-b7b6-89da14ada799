{% extends "homepage/base.html" %}
{% load static %}
{% load widget_tweaks %}

{% block content %}
  <section class="mx-4">
    <h1 class="pt-4 mb-4 text-center text-4xl">Нова Оферта</h1>
    <form novalidate method="post" enctype="multipart/form-data">
      {% csrf_token %}

      <div class="w-full max-w-2xl mx-auto">
        {% for field in form %}
          {% if field.name == 'is_active' %}
            <c-form-checkbox />
          {% elif field.name == 'top_offer' %}
            <c-form-checkbox />

            <!-- IF main_image -->
          {% elif field.name == 'main_image' %}
            <label class="block text-gray-700 text-xl">
              {{ field.label }} <span class="text-red-400 text-xl">*</span>
            </label>
            <div class="mb-8 p-8 flex flex-col items-center sm:flex-row sm:justify-between gap-8 border border-slate-100 shadow-md">
              <h2 class="text-2xl leading-none"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> Снимка</h2>
              <div class="w-40 h-40">
                <img id="main-image-preview" class="size-full object-cover cursor-pointer" />
              </div>
              <div class="flex flex-col items-center justify-center gap-4">
                <label id="main-image-label"
                       for="main-image"
                       class="cursor-pointer bg-green-600 text-white px-4 py-2 rounded">Избери Заглавна Снимка</label>
                <input id="main-image"
                       type="file"
                       accept="image/*"
                       name="{{ field.name }}"
                       class="hidden" />
              </div>
            </div>

            <!-- The other images custom form -->
            <c-upload-multiple-images-form />
          {% else %}
            <div class="flex gap-1">
              <!-- Labels -->
              <label class="block text-gray-700 text-xl">{{ field.label }}</label>
              {% if field.field.required is True %}<p class="text-red-400 text-xl">*</p>{% endif %}
            </div>

            <!-- Error handling -->
            <p class="text-red-400 text-xl">{{ field.errors.0 }}</p>
            {% render_field field class='input mb-4' %}
          {% endif %}
        {% endfor %}

        <c-button-default addclass="w-full mb-8">
        Пусни Оферта
        </c-button-default>
      </div>
    </form>
  </section>
{% endblock content %}

{% block scripts %}
  <script src="{% static 'js/fileUploadForm.js' %}"></script>
{% endblock scripts %}
