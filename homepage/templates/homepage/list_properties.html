{% extends "homepage/base.html" %}
{% load static %}
{% load widget_tweaks %}

{% block content %}
  <section class="mb-8">

    <div class="flex justify-center">
      <div class="w-full max-w-4xl">
        <form action="{% if active_properties == True %} {% url 'list_properties' %} {% else %} {% url 'list_inactive_properties' %} {% endif %}"
              method="post"
              class="w-full flex justify-center p-4">

          {% csrf_token %}

          <div class="max-w-lg w-full">
            <div class="w-3xl flex justify-between mb-4">
              <!-- Button Filter -->
              <button type="button"
                      onclick="toggleMobileSearch()"
                      class="w-40 h-12 py-2 px-2 flex items-center justify-between border border-slate-500 rounded-md hover:shadow-md duration-300">
                Филтри
                <c-icons.slider class="size-6" />
              </button>

              <!-- SORT -->
              <div class="relative flex items-center">
                {% render_field form.sort id="sort-select" class='w-40 h-12 py-2 px-2 flex justify-between border border-slate-500 rounded-md hover:shadow-md duration-300 focus:ring-0 focus:border-slate-500 hover:cursor-pointer' %}
                <c-icons.sort class="absolute right-2 size-6 bg-white" />
              </div>
            </div>

            <!-- SEARCH + BUTTON -->
            <div class="w-full flex">
              {% comment %} <c-form-input :field="form.query" /> {% endcomment %}
              {% render_field form.query class="w-full rounded-l-md border-slate-300 focus:ring-0 focus:border-anri-color" %}
              <button type="submit" class="btn rounded-l-none">Търси</button>
            </div>
          </div>

          <!-- Mobile Full Screen Search -->
          <!-- It is HIDDEN (-translate-y-full ) -->
          <div id="mobile-search-menu"
               class="-translate-y-full bg-neutral-50 fixed top-0 left-0 w-full h-svh shadow-lg transform transition-transform duration-500 z-50 overflow-y-auto">
            <div class="w-full p-4 flex justify-end">
              <button id="close-btn">
                <c-icons.x class="size-8" />
              </button>
            </div>

            <div class="px-4">
              <a href="{% if active_properties == True %} {% url 'list_properties' %} {% else %} {% url 'list_inactive_properties' %} {% endif %}"
                 class="mb-4 inline-block btn bg-red-800">Изчисти</a>

              <div id="filter">
                <c-form-input :field="form.service_type"></c-form-input>
                <c-form-input :field="form.property_type"></c-form-input>
                <c-form-input :field="form.district"></c-form-input>
                <div class="flex gap-4 justify-between">
                  <c-form-input :field="form.price_from"></c-form-input>
                  <c-form-input :field="form.price_to"></c-form-input>
                </div>
                <div class="flex gap-4 justify-between">
                  <c-form-input :field="form.area_from"></c-form-input>
                  <c-form-input :field="form.area_to"></c-form-input>
                </div>
                <c-form-input :field="form.heating"></c-form-input>
                <c-form-input :field="form.structure"></c-form-input>

                <p class="text-2xl pt-4 text-center">Етажи</p>
                <div class="mb-8 flex gap-4 justify-between">
                  <div>
                    <c-form-input :field="form.floor_from"></c-form-input>
                    <c-form-checkbox :field="form.not_first_floor"></c-form-checkbox>
                  </div>
                  <div>
                    <c-form-input :field="form.floor_to"></c-form-input>
                    <c-form-checkbox :field="form.not_last_floor"></c-form-checkbox>
                  </div>
                </div>
              </div>

              <button id="mobile-search-button" type="submit" class="btn w-full mb-4">Търси</button>

            </div>
          </div>
        </form>

        <div id="pagination-jump-point" class="-translate-y-12"></div>

        {% if active_properties == True %}
          <!-- TEXT + ICON before results -->
          <div class=" w-full flex flex-col items-center justify-center">
            {% if properties|length == 0 %}<c-icons.nothing-found class="inline-block" />{% endif %}

            <h1 class="max-w-lg pt-12 mb-8 text-4xl text-center leading-none">
              {% if properties|length == 0 %}
                Няма резултати. Опитайте с други параметри.
              {% elif properties|length == 1 %}
                Имаме {{ properties|length }} попадение за Вас
              {% else %}
                Имаме {{ properties|length }} попадения за Вас
              {% endif %}
            </h1>
          </div>
        {% else %}
          <h1 class="my-8 text-4xl text-red-600 text-center leading-none">
            {% if properties|length == 0 %}
              Всички оферти са активни.
            {% elif properties|length == 1 %}
              Имате {{ properties|length }} неактивна оферта.
            {% else %}
              Имате {{ properties|length }} неактивни оферти.
            {% endif %}
          </h1>

        {% endif %}

        <!-- RESULTS -->
        <div class="mb-8 px-4 grid grid-cols-[repeat(auto-fit,minmax(20rem,1fr))] gap-4 justify-center">
          {% for property in page_obj %}
            <c-property-card :property="property">
            </c-property-card>
          {% endfor %}
        </div>

        <c-pagination />

      </div>
    </div>
  </section>

  <script>
    function toggleMobileSearch() {
      const mobileSearchMenu = document.getElementById('mobile-search-menu');
      mobileSearchMenu.classList.toggle("-translate-y-full");
      document.body.classList.toggle("overflow-hidden");
    };

    document.getElementById('sort-select').addEventListener('change', function() {
      document.getElementById('mobile-search-button').click();
  });
  </script>
{% endblock content %}
