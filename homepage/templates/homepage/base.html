{% load static tailwind_tags %}
{% load static %}
<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Anri Estate</title>
    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link href="https://fonts.googleapis.com/css2?family=Noto+Serif:ital,wght@0,700;1,700&family=Sofia+Sans:ital,wght@0,1..1000;1,1..1000&family=Roboto:ital,wght@0,100;0,300;0,400;0,500;0,700;0,900;1,100;1,300;1,400;1,500;1,700;1,900&display=swap"
          rel="stylesheet" />
    <!-- Bootstrap Icons -->
    <link rel="stylesheet"
          href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.min.css" />
    {% tailwind_css %}
    <link rel="icon"
          href="{% static 'icons/favicon.ico' %}"
          type="image/x-icon" />
    <script defer
            src="https://cdn.jsdelivr.net/npm/alpinejs@3.14.1/dist/cdn.min.js"></script>
  </head>
  <body class="min-h-[100vh] grid grid-rows-[auto,1fr,auto]">
    {% comment %} {% endcomment %}
    {% include "homepage/navbar.html" %}

    {% block content %}
    {% endblock content %}

    {% include "homepage/footer.html" %}

    {% block scripts %}
    {% endblock scripts %}
    <script src="{% static 'js/base.js' %}"></script>
    <script src="{% static 'js/htmx.min.js' %}"></script>
    <script>
      document.body.addEventListener('htmx:configRequest', (event) => {
        event.detail.headers['X-CSRFToken'] = '{{ csrf_token }}'
      })
    </script>
  </body>
</html>
