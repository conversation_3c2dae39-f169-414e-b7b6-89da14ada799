{% extends "homepage/base.html" %}

{% block content %}
  <section class="p-4">
    <form class="card py-4 px-2 mb-8"
          hx-on::after-request="if(event.detail.xhr.status == 222) this.reset()">
      {% csrf_token %}

      <p class="pt-2">Добави {{ verbose_name }}</p>
      <div class="flex items-center gap-2">
        <input type="text"
               name="{{ type }}_name"
               class="input"
               placeholder="Добави {{ verbose_name }}"
               hx-post="/{{ type }}_check/"
               hx-swap="outerhtml"
               hx-trigger="keyup delay:0.3s"
               hx-target="#{{ type }}-error" />
        <button hx-post="{% url 'generic_add' type %}"
                hx-target="#{{ type }}-list"
                onclick="this.disabled=true; setTimeout(() => this.disabled=false, 1000)"
                type="submit"
                class="btn py-2 px-4 text-xl">Запиши</button>
      </div>
      <div id="{{ type }}-error" class="text-red-400 min-h-6"></div>
    </form>

    <div id="{{ type }}-list">{% include "homepage/generics/generic_list.html" %}</div>
  </section>
{% endblock content %}
