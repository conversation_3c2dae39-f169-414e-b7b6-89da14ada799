{% load widget_tweaks %}

<form class="size-full flex items-center justify-between border border-slate-100 shadow-sm hover:border-slate-200 hover:shadow-md transition-all duration-300"
      hx-put="{% url 'generic_update' type model_instance.id %}"
      hx-target="#{{ type }}-list">
  {% csrf_token %}
  {% render_field form.name class='py-1 px-2 w-full text-xl text-green-600 border-none shadow-none focus:ring-0' %}

  <div class="p-2 flex gap-2">
    <button type="submit">
      <c-icons.check class="p-1 size-8 text-green-600 border border-green-600 rounded-md"
                     hx-get="{% url 'generic_edit' type model_instance.id %}"
                     hx-target="#edit-field-{{ model_instance.id }}" />

    </button>
  </div>
</form>
