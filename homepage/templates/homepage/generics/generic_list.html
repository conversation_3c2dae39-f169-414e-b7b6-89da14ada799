{% for object in objects %}
  <div id="edit-field-{{ object.id }}"
       class="mb-2 w-full flex items-center justify-between border border-slate-100 shadow-sm hover:border-slate-200 hover:shadow-md transition-all duration-300">
    <p class="py-1 px-2 text-xl">{{ object.name }}</p>
    <div class="p-2 flex gap-2">
      <c-icons.pencil class="p-1 size-8 text-blue-400 border border-blue-400 rounded-md"
                      hx-get="{% url 'generic_edit' type object.id %}"
                      hx-target="#edit-field-{{ object.id }}" />
      <c-icons.x class="size-8 text-red-400 border border-red-400 rounded-md"
                 hx-delete="{% url 'generic_delete' type object.id %}"
                 hx-target="#{{ type }}-list"
                 hx-confirm="Сигурни ли сте, че искате да изтирете {{ object.name }}?" />
    </div>
  </div>
{% endfor %}
