{% extends "homepage/base.html" %}
{% load static %}
{% load widget_tweaks %}

{% block content %}
  <section class="m-4">
    <h1 class="text-center text-4xl">Редакция на Брокер</h1>
    <form action="" method="post" enctype="multipart/form-data">
      {% csrf_token %}

      <div class="w-full max-w-2xl mx-auto">
        {% for field in form %}
          <!-- IF agent_image -->
          {% if field.name == 'image' %}
            <label class="block text-gray-700 text-xl">{{ field.label }}</label>
            <div class="mb-8 p-8 flex flex-col items-center sm:flex-row sm:justify-between gap-8 border border-slate-100 shadow-md">
              <div class="w-40 h-48">
                {% if agent.image %}
                  <img id="agent-image-preview"
                       class="size-full object-cover text-slate-300 bg-slate-50 shadow-md rounded-md"
                       src="{{ agent.image.url }}" />
                {% else %}
                  <c-icons.default-user />
                {% endif %}
              </div>
              <div class="flex flex-col items-center justify-center gap-4">
                <label id="agent-image-label"
                       for="agent-image"
                       class="cursor-pointer bg-blue-600 text-white px-4 py-2 rounded">Промени Снимка</label>
                <input id="agent-image"
                       type="file"
                       accept="image/*"
                       name="{{ field.name }}"
                       class="hidden" />
                <span id="agent-image-chosen" class="ml-2"></span>
              </div>
            </div>
          {% else %}
            <p class="text-red-400 text-xl">{{ field.errors.0 }}</p>
            <div class="flex gap-1">
              <label class="block text-gray-700 text-xl">{{ field.label }}</label>
              <p class="text-red-400 text-xl">{{ field.help_text }}</p>
            </div>
            {{ field|add_class:'mb-4 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500' }}
          {% endif %}
        {% endfor %}

        <c-button-default type="submit" addclass="w-full mb-4">
        Запази Промените
        </c-button-default>
      </div>
    </form>
    <!-- Delete Agent -->
    <c-button-danger hx-delete="{% url 'delete_agent' agent.id %}" hx-confirm="Сигурни ли сте, че искате да изтирете брокер {{ agent }}?">
    Изтрий Офертата
    </c-button-danger>

  </section>
{% endblock content %}

{% block scripts %}
  <script src="{% static 'js/agentUploadForm.js' %}"></script>
{% endblock scripts %}
