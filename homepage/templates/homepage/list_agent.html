{% extends "homepage/base.html" %}
{% load static %}
{% load custom_filters %}

{% block content %}
  <div class="p-4 w-full">
    <!-- Add Agent Button -->
    {% if user.is_authenticated %}
      <div class="w-full flex justify-end">
        <a href="{% url 'new_agent' %}"
           class="mb-4 py-2 px-8 flex items-center gap-2 bg-red-500 text-slate-50 text-xl font-bold rounded-md">
          <span class="text-3xl">+</span>
          Брокер
        </a>
      </div>
    {% endif %}

    <h1 class="mb-4 text-3xl text-center">Нашите Брокери</h1>
    <div class="grid grid-cols-[repeat(auto-fit,minmax(20rem,1fr))] gap-4">
      {% for agent in agents %}
        <div class="w-80 bg-white mx-auto mb-4">
          <!-- AGENT CARD -->
          <c-agent-card>
          </c-agent-card>
        </div>
      {% endfor %}
    </div>
  </div>
{% endblock content %}
