{% load static %}
{% load widget_tweaks %}

<nav id="main-navigation"
     class="p-4 flex justify-center items-center bg-neutral-50 shadow-md sticky top-0 z-50">
  <div class="w-full max-w-4xl flex justify-between items-center">
    <!-- LOGO -->
    <div>
      <a href="{% url 'home' %}">
        <div class="flex items-center gap-2">
          <img src="{% static 'images/anri_logo.jpg' %}" alt="Logo Image" />
          <div class="text-2xl">
            <span class="text-anri-color font-bold">Anri</span>
            <span class="text-anri-color-red">Estate</span>
          </div>
        </div>
      </a>
    </div>

    <!-- Middle Navbar Section -->
    <div class="hidden md:flex">
      <ul class="flex md:gap-4 lg:gap-6 font-light">
        {% include "homepage/includes/navlinks_public.html" %}
      </ul>
    </div>

    <!-- Navbar Search -->
    <div class="flex items-center gap-2">
      <div class="flex items-center gap-2">

        <!-- Search Icon -->
        <div class="flex items-center">
          <button id="searchButton" hx-on="click: toggleVisibility()">
            <c-icons.search class="size-6" />
          </button>
          <div id="searchForm" class="hidden absolute w-full -bottom-10 left-0">
            <div class="w-full flex justify-center">
              <form action="{% url "list_properties" %}"
                    method="post"
                    class="flex items-center">
                {% csrf_token %}
                <div class="flex">
                  {% render_field global_search_form.query class="px-4 py-2 text-xl rounded-l-md border border-gray-300 focus:outline-none focus:ring-0 focus:border-anri-color" %}
                  <button type="submit"
                          class="px-4 py-2 text-xl rounded-r-md bg-anri-color text-white">Търси</button>
                </div>
              </form>
            </div>
          </div>
        </div>

        <!-- Hello User -->
        {% if user.is_authenticated %}
          <ul class="relative hidden md:block group hover:cursor-pointer">
            <div class="flex items-center gap-1">
              <li class="font-light text-red-800 group-hover:font-medium">Здравейте {{ user.first_name }}</li>
              <c-icons.chevron-down class="size-4" />
            </div>
            <div class="hidden absolute right-0 w-52 font-light card p-4 group-hover:flex flex-col gap-2">
              {% include "homepage/includes/navlinks_user.html" %}
            </div>
          </ul>
        {% endif %}

        <!-- User Icon -->
        {% if user.is_authenticated %}
          <div id="user-icon" class="md:hidden">
            <form method="post" action="{% url 'logout' %}">
              {% csrf_token %}
              <div class="relative">
                <div id="logged-in-icon"
                     class="bg-white rounded-full p-2 border border-slate-200 cursor-pointer">
                  <c-icons.user class="size-6 text-red-800" />
                </div>
              </div>
            </form>
          </div>
        {% else %}
          <!-- Hamburger Button -->
          <div class="md:hidden flex items-center">
            <button id="hamburger-button">
              <c-icons.hamburger-menu class="size-8" />
            </button>
          </div>
        {% endif %}
      </div>

      <!-- Mobile Nav Menu -->
      <div id="mobile-menu"
           class="fixed top-0 right-0 w-full h-lvh overflow-y-auto bg-slate-700 text-slate-200 shadow-lg transform translate-x-full transition-transform duration-500">
        <div class="w-full py-6 pr-4 flex justify-end">
          <button>
            <c-icons.x class="size-8 font-bold" />
          </button>
        </div>
        <nav class="flex flex-col h-svh items-center text-3xl" aria-label="mobile">
          <ul class="grid gap-2">
            {% include "homepage/includes/navlinks_public.html" %}
            <div class="py-4">
              <hr />
            </div>
            {% include "homepage/includes/navlinks_user.html" %}
          </ul>
        </nav>
      </div>
    </div>
  </div>

  <!-- SEARCH Script-->
  <script>
    const form = document.getElementById('searchForm')
    const input = document.querySelector('input[name=query]')
    const searchButton = document.getElementById('searchButton')
    const heroSearchInput = document.getElementById('heroSearchInput')
    
    function toggleVisibility() {
      form.classList.toggle('hidden')
      input.focus()
    }
    
    document.addEventListener('keyup', function (event) {
      if (event.key === '/' && heroSearchInput !== document.activeElement) {
        form.classList.remove('hidden')
        input.focus()
      }
    })
    
    // Hide the search field by clicking outside of it
    document.addEventListener('click', function (event) {
      if (!form.contains(event.target) && !searchButton.contains(event.target)) {
        form.classList.add('hidden')
      }
    })
  </script>

</nav>
