{% extends "homepage/base.html" %}
{% load static %}
{% load widget_tweaks %}

{% block content %}
  <section class="mx-4">
    <h1 class="text-center text-4xl">Обработване на оферта № {{ property.offer_nr }}</h1>

    <form action="" method="post" enctype="multipart/form-data">
      {% csrf_token %}

      <div class="mb-8 w-full max-w-2xl mx-auto">
        {% for field in form %}
          {% if field.name == 'is_active' %}
            <c-form-checkbox />
          {% elif field.name == 'top_offer' %}
            <c-form-checkbox />

            <!-- IF main_image -->
          {% elif field.name == 'main_image' %}
            <!-- Main Image -->
            <div>
              <label class="block text-gray-700 text-xl">{{ field.label }}</label>
              <div class="mb-8 p-8 flex flex-col items-center sm:flex-row sm:justify-between gap-8 border border-slate-100 shadow-md">
                <div class="w-40 h-40">
                  <img id="main-image-preview"
                       class="size-full object-cover"
                       src="{{ property.main_image.url }}" />
                </div>
                <div class="flex flex-col items-center justify-center gap-4">
                  <label id="main-image-label"
                         for="main-image"
                         class="cursor-pointer bg-green-600 text-white px-4 py-2 rounded">
                    Промени Заглавната Снимка
                  </label>
                  <input id="main-image"
                         type="file"
                         accept="image/*"
                         name="{{ field.name }}"
                         class="hidden" />
                </div>
              </div>
            </div>

            <!-- Image Gallery -->
            <div class="mt-12">
              <h2 class="w-full text-2xl text-center">Снимки</h2>
              <div class="px-4 py-8 grid grid-cols-[repeat(auto-fit,minmax(11rem,1fr))] border border-slate-200 shadow-md">
                {% for photo in property.photos.all %}
                  <div class="flex flex-col items-center gap-0">
                    <div class="w-40 h-48 overflow-hidden">
                      <img src="{{ photo.image.url }}" alt="" class="size-full object-cover" />
                    </div>
                    <div>
                      <form action="{% url 'delete_photo' id=property.id photo_id=photo.id %}"
                            method="post">
                        {% csrf_token %}
                        <button type="submit" class="mt-1 mb-4 px-4 py-2 bg-slate-200 rounded">Изтрий</button>
                      </form>
                    </div>
                  </div>
                {% endfor %}
              </div>
            </div>

            <!-- The other images custom form -->
            <c-upload-multiple-images-form />

            <!-- ALL THE OTHER FIELDS -->
          {% else %}
            <p class="text-red-400 text-xl">{{ field.errors.0 }}</p>
            <div class="flex gap-1">
              <label class="block text-gray-700 text-xl">{{ field.label }}</label>
              <p class="text-red-400 text-xl">{{ field.help_text }}</p>
            </div>
            {% comment %} {{ field|add_class:'mb-4 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500' }} {% endcomment %}
            {% render_field field class='mb-4 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500' %}
          {% endif %}
        {% endfor %}

        <c-button-default addclass="w-full">
        Запази промените
        </c-button-default>
      </div>
    </form>

    <!-- DELETE PROPERTY -->
    <div class="mb-12 w-full flex justify-center">
      <c-button-danger hx-delete="{% url 'delete_property' property.id %}" hx-confirm="Сигурни ли сте, че искате да изтирете Оферта № {{ property }}?">
      Изтрий Офертата
      </c-button-danger>
    </div>
  </section>
{% endblock content %}

{% block scripts %}
  <script src="{% static 'js/fileUploadForm.js' %}"></script>
  <script src="{% static 'js/jumpBackPosition.js' %}"></script>
{% endblock scripts %}
