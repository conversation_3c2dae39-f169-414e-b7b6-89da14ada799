from django.urls import path

from . import views

urlpatterns = [
    path("", views.home, name="home"),
    path(
        "properties",
        views.list_active_properties,
        name="list_properties",
    ),
    path(
        "properties/inactive",
        views.list_inactive_properties,
        name="list_inactive_properties",
    ),
    path(
        "property/<uuid:id>",
        views.single_active_property,
        name="single_active_property",
    ),
    path(
        "property/inactive/<uuid:id>",
        views.single_inactive_property,
        name="single_inactive_property",
    ),
    path("property/new", views.new_property, name="new_property"),
    path("property/edit/<uuid:id>", views.edit_property, name="edit_property"),
    path("property/delete/<uuid:id>", views.delete_property, name="delete_property"),
    path(
        "property/<uuid:id>/delete_photo/<int:photo_id>/",
        views.delete_photo,
        name="delete_photo",
    ),
    path("agents/", views.list_agent, name="list_agent"),
    path("agent/<int:id>", views.agent_detail, name="agent_detail"),
    path("new-agent/", views.new_agent, name="new_agent"),
    path("agent/edit/<int:id>", views.edit_agent, name="edit_agent"),
    path(
        "delete_agent/<int:id>/",
        views.delete_agent,
        name="delete_agent",
    ),
    path("uslugi/", views.uslugi, name="uslugi"),
    path("contact/", views.contact, name="contact"),
    path("about_us/", views.about_us, name="about_us"),
    path("settings/", views.settings, name="settings"),
]


htmx_urlpatterns = [
    path("<str:type>_add/", views.generic_add, name="generic_add"),
    path("<str:type>_check/", views.generic_check, name="generic_check"),
    path("<str:type>_delete/<int:id>/", views.generic_delete, name="generic_delete"),
    path("<str:type>_edit/<int:id>/", views.generic_edit, name="generic_edit"),
    path("<str:type>_update/<int:id>/", views.generic_update, name="generic_update"),
    path("<str:type>/", views.generic_list, name="generic_list"),
]

urlpatterns += htmx_urlpatterns
