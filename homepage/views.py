from django.core.paginator import Paginator
from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.urls import reverse_lazy, reverse
from django.db.models import Q, F, ProtectedError
from django.http import JsonResponse, QueryDict, HttpResponse

from .models import (
    Property,
    Photo,
    Agent,
    ServiceType,
    PropertyType,
    Location,
    District,
    Currency,
    Heating,
    Structure,
)
from .forms import (
    PropertyForm,
    AgentForm,
    SearchForm,
    ServiceTypeForm,
    PropertyTypeForm,
    LocationForm,
    DistrictForm,
    CurrencyForm,
    HeatingForm,
    StructureForm,
)


from .utils import USLUGI

MODEL_MAP = {
    "service_type": ServiceType,
    "property_type": PropertyType,
    "location": Location,
    "district": District,
    "currency": Currency,
    "heating": Heating,
    "structure": Structure,
}

FORM_MAP = {
    "service_type": ServiceTypeForm,
    "property_type": PropertyTypeForm,
    "location": LocationForm,
    "district": DistrictForm,
    "currency": CurrencyForm,
    "heating": HeatingForm,
    "structure": StructureForm,
}

SORT_MAP = {
    "": "-created",
    "price_asc": "price",
    "price_desc": "-price",
    "date_asc": "created",
    "date_desc": "-created",
    "area_asc": "area",
    "area_desc": "-area",
}


# Pagination
def get_pagination(request, model_data):
    paginator = Paginator(model_data.order_by("-created"), 6)
    num_of_pages = paginator.num_pages
    curr_page = request.GET.get("page")

    pagination_obj = {
        "page_obj": paginator.get_page(curr_page),
        "range_list": paginator.page_range,
    }

    return pagination_obj


# Create your views here.
def home(request):
    top_offers = Property.objects.filter(top_offer=True, is_active=True)

    pagination_obj = get_pagination(request, top_offers)
    context = {
        "page_obj": pagination_obj["page_obj"],
        "range_list": pagination_obj["range_list"],
    }
    return render(request, "homepage/index.html", context)


def get_query_filter(query, active_properties):
    query_words = query.split()
    q_filter = Q(is_active=active_properties)

    for word in query_words:
        q_filter &= (
            Q(offer_nr__icontains=word)
            | Q(heading__icontains=word)
            | Q(service_type__name__icontains=word)
            | Q(property_type__name__icontains=word)
            | Q(location__name__icontains=word)
            | Q(district__name__icontains=word)
            | Q(heating__name__icontains=word)
            | Q(structure__name__icontains=word)
            | Q(description__icontains=word)
            | Q(location_description__icontains=word)
            | Q(building_description__icontains=word)
            | Q(condition__icontains=word)
            | Q(room_distribution__icontains=word)
            | Q(additional_information__icontains=word)
        )

    return q_filter


def list_properties(request, heading_text, active_properties):
    if request.method == "POST":
        form = SearchForm(request.POST)
        if form.is_valid():
            # Get the properties
            properties_objects = Property.objects.all().filter(
                is_active=active_properties
            )

            filter_kwargs = {
                "service_type": form.cleaned_data["service_type"],
                "property_type": form.cleaned_data["property_type"],
                "district": form.cleaned_data["district"],
                "heating": form.cleaned_data["heating"],
                "structure": form.cleaned_data["structure"],
                "price__gte": form.cleaned_data["price_from"],
                "price__lte": form.cleaned_data["price_to"],
                "area__gte": form.cleaned_data["area_from"],
                "area__lte": form.cleaned_data["area_to"],
                "floor__gte": form.cleaned_data["floor_from"],
                "floor__lte": form.cleaned_data["floor_to"],
            }

            filter_kwargs = {k: v for k, v in filter_kwargs.items() if v}

            query = form.cleaned_data["query"]
            sort = form.cleaned_data["sort"]
            if sort == "":
                sort = "-created"

            not_first_floor = form.cleaned_data.get("not_first_floor")
            if not_first_floor:
                properties_objects = properties_objects.exclude(floor=1)

            not_last_floor = form.cleaned_data.get("not_last_floor")
            if not_last_floor:
                properties_objects = properties_objects.exclude(floor=F("floor_count"))

            if query:
                query_filter = get_query_filter(query, active_properties)
                properties = properties_objects.filter(
                    query_filter, **filter_kwargs
                ).order_by(sort)
            else:
                properties = properties_objects.filter(**filter_kwargs).order_by(sort)
    if request.method == "GET":
        # If active_properties=True then all the active offers are find
        # and vice versa
        properties = Property.objects.filter(is_active=active_properties)
        form = SearchForm()

    pagination_obj = get_pagination(request, properties)

    context = {
        "properties": properties,
        "form": form,
        "page_obj": pagination_obj["page_obj"],
        "range_list": pagination_obj["range_list"],
        "heading_text": heading_text,
        "active_properties": active_properties,
    }
    return render(request, "homepage/list_properties.html", context)


def list_active_properties(request):
    return list_properties(request, "", active_properties=True)


@login_required
def list_inactive_properties(request):
    return list_properties(request, "Inactive Heading", active_properties=False)


@login_required
def new_property(request):
    if request.method == "POST":
        form = PropertyForm(request.POST, request.FILES)
        images = request.FILES.getlist("images")

        if form.is_valid():
            property = form.save()
            for image in images:
                Photo.objects.create(property=property, image=image)

            return redirect(reverse_lazy("single_active_property", args=[property.id]))
        else:
            context = {
                "form": form,
                "images": images,
            }
            return render(request, "homepage/new_property.html", context)

    context = {"form": PropertyForm()}
    return render(request, "homepage/new_property.html", context)


@login_required
def edit_property(request, id):
    property = get_object_or_404(Property, id=id)
    if request.method == "POST":
        form = PropertyForm(request.POST, request.FILES, instance=property)
        images = request.FILES.getlist("images")

        if form.is_valid():
            form.save()
            # Handle new images
            for image in images:
                Photo.objects.create(property=property, image=image)

            current_url = request.get_full_path()
            if "inactive" in current_url:
                return redirect("list_inactive_properties")
            else:
                return redirect("list_properties")

        else:
            context = {
                "form": form,
                "property": property,
                "images": images,
            }
            return render(request, "homepage/edit_property.html", context)

    else:
        form = PropertyForm(instance=property)
        context = {
            "form": form,
            "property": property,
        }
        return render(request, "homepage/edit_property.html", context)


@login_required
def delete_property(request, id):
    property = get_object_or_404(Property, id=id)
    property.delete()
    response = JsonResponse({"location": reverse("list_properties")})
    response["HX-Redirect"] = reverse("list_properties")
    return response


@login_required
def delete_photo(request, id, photo_id):
    photo = get_object_or_404(Photo, id=photo_id, property_id=id)
    photo.delete()
    return redirect(reverse("edit_property", args=[id]))


def single_property(request, id, active_properties):
    print(active_properties)
    property = get_object_or_404(Property, id=id, is_active=active_properties)
    photos = property.photos.all()
    context = {"property": property, "photos": photos}
    return render(request, "homepage/single_property.html", context)


def single_active_property(request, id):
    return single_property(request, id, active_properties=True)


@login_required
def single_inactive_property(request, id):
    return single_property(request, id, active_properties=False)


def list_agent(request):
    agents = Agent.objects.all()
    context = {"agents": agents}
    return render(request, "homepage/list_agent.html", context)


def agent_detail(request, id):
    agent = get_object_or_404(Agent, id=id)
    properties = Property.objects.filter(agent=agent)
    context = {"agent": agent, "properties": properties}
    return render(request, "homepage/agent_detail.html", context)


@login_required
def new_agent(request):
    if request.method == "POST":
        form = AgentForm(request.POST, request.FILES)

        if form.is_valid():
            agent = form.save()
            return redirect("list_agent")
        else:
            context = {"form": form}
            return render(request, "homepage/new_agent.html", context)

    context = {"form": AgentForm()}
    return render(request, "homepage/new_agent.html", context)


@login_required
def edit_agent(request, id):
    agent = get_object_or_404(Agent, id=id)
    if request.method == "POST":
        form = AgentForm(request.POST, request.FILES, instance=agent)

        if form.is_valid():
            form.save()

            return redirect("list_agent")
        else:
            context = {
                "form": form,
                "agent": agent,
            }
            return render(request, "homepage/edit_agent.html", context)

    form = AgentForm(instance=agent)
    context = {"form": form, "agent": agent}
    return render(request, "homepage/edit_agent.html", context)


@login_required
def delete_agent(request, id):
    agent = get_object_or_404(Agent, id=id)
    agent.delete()
    response = JsonResponse({"location": reverse("list_agent")})
    response["HX-Redirect"] = reverse("list_agent")
    return response


# SETTINGS
@login_required
def settings(request):
    return render(request, "homepage/settings.html")


@login_required
def generic_list(request, type):
    Model = MODEL_MAP[type]
    verbose_name = Model._meta.get_field("name").verbose_name

    objects = Model.objects.all()
    context = {"objects": objects, "type": type, "verbose_name": verbose_name}
    return render(request, "homepage/generics/generic.html", context)


@login_required
def generic_add(request, type):
    name = request.POST.get(f"{type}_name").strip()
    if not name:
        return HttpResponse(f"{type.capitalize()} name cannot be empty", status=400)

    Model = MODEL_MAP[type]
    model_instance = Model.objects.create(name=name)
    model_instance.save()

    objects = Model.objects.all()
    context = {"objects": objects, "type": type}

    ### Need the status=222 in conjunction with check_location so I can check
    ### if the location exists but not trigger the 'this.reset()' function
    ### in 'locations.html'
    ### The form reset is triggered only if status=222
    return render(request, "homepage/generics/generic_list.html", context, status=222)


@login_required
def generic_check(request, type):
    name = request.POST.get(f"{type}_name").strip()
    Model = MODEL_MAP[type]

    if not name:
        return HttpResponse("<div>Полето е празно</div>")
    elif Model.objects.filter(name=name).exists():
        verbose_name = Model._meta.get_field("name").verbose_name
        gender = Model._meta.get_field("gender").verbose_name

        return HttpResponse(
            f"<div>{gender.title()} {verbose_name} съществува вече</div>"
        )
    else:
        return HttpResponse("<div> </div>")


@login_required
def generic_delete(request, type, id):
    Model = MODEL_MAP[type]
    object = Model.objects.get(id=id)
    try:
        object.delete()
        objects = Model.objects.all()
        context = {"objects": objects, "type": type}
        return render(request, "homepage/generics/generic_list.html", context)
    except ProtectedError as e:
        protected_objects = e.protected_objects
        sorted_objects = sorted(protected_objects, key=lambda x: x.offer_nr)

        html_code = "<ul class='list-disc ml-8'>"
        for el in sorted_objects:
            html_code += f"<li>Оферта № {el}</li>"
        html_code += "</ul>"
        error_message = (
            f"{object} не може да бъде изтрит защото се използва от: {html_code}"
        )

        edit_url = reverse("generic_list", args=[type])

        return HttpResponse(
            f"<div class='text-red-400 min-h-6'>{error_message}</div><div class='w-full flex justify-center'><a href='{edit_url}' class='btn inline-block mt-4'>OK</a></div>"
        )


@login_required
def generic_edit(request, type, id):
    Model = MODEL_MAP[type]
    Form = FORM_MAP[type]
    model_instance = get_object_or_404(Model, id=id)
    form = Form(instance=model_instance)

    context = {"type": type, "model_instance": model_instance, "form": form}
    return render(request, "homepage/generics/generic_edit.html", context)


@login_required
def generic_update(request, type, id):
    Model = MODEL_MAP[type]
    Form = FORM_MAP[type]
    model_instance = get_object_or_404(Model, id=id)

    if request.method == "PUT":
        data = QueryDict(request.body).dict()
        form = Form(data, instance=model_instance)
        if form.is_valid():
            form.save()

            objects = Model.objects.all()
            context = {"objects": objects, "type": type}
            return render(request, f"homepage/generics/generic_list.html", context)


def uslugi(request):
    context = {"items": USLUGI}
    return render(request, "homepage/uslugi.html", context)


def contact(request):
    return render(request, "homepage/contact.html")


def about_us(request):
    return render(request, "homepage/about_us.html")
