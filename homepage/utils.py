import random, string

from django.core.files.base import ContentFile

from PIL import Image, ImageDraw, ImageFont, ExifTags
from io import BytesIO


def resize_and_watermark(
    image_field,
    image_name,
    font_size=50,
    watermark_text="",
    target_pixel_size=0,
):
    img = Image.open(image_field)

    # Orientation
    try:
        for orientation in ExifTags.TAGS.keys():
            if ExifTags.TAGS[orientation] == "Orientation":
                break

        exif = img.getexif()

        if exif is not None:
            orientation_value = exif.get(orientation)
            if orientation_value == 3:
                img = img.rotate(180, expand=True)
            elif orientation_value == 6:
                img = img.rotate(270, expand=True)
            elif orientation_value == 8:
                img = img.rotate(90, expand=True)
    except (AttributeError, KeyError, IndexError):
        # Handle cases where the image doesn't have EXIF data or the orientation tag is missing
        pass

    # Resize the image
    # ...only if target_pixel_size is given
    if target_pixel_size:
        width, height = img.size
        if width > height:
            factor = round(width / target_pixel_size)
        else:
            factor = round(height / target_pixel_size)
        img = img.resize((int(width / factor), int(height / factor)))

    # Add watermark
    # only if watermark_text is given
    if watermark_text:
        opacity = 150  # Adjust opacity as needed (0 to 255)
        watermark_image = Image.new("RGBA", img.size, (0, 0, 0, 0))
        watermark_font = ImageFont.load_default(font_size)

        draw = ImageDraw.Draw(watermark_image)

        text_width = draw.textlength(watermark_text, font=watermark_font)
        text_position = (
            int(img.width / 2 - (text_width / 2)),
            int(img.height / 2 - (font_size / 2)),
        )
        draw.text(
            text_position,
            watermark_text,
            font=watermark_font,
            fill=(255, 255, 255, opacity),
        )

        img.paste(watermark_image, mask=watermark_image)

    # Save the modified image to a BytesIO object
    output = BytesIO()
    img.save(output, format="JPEG")
    output.seek(0)

    image_field.save(image_name, ContentFile(output.read()), save=False)


def generate_random_string(length=7):
    characters = string.ascii_letters + string.digits
    return "".join(random.choice(characters) for _ in range(length))


########################
# Libraries

property_model_fields = {
    "offer_nr": "Оферта №",
    "top_offer": "Топ Оферта?",
    "main_image": "Заглавна Снимка",
    "heading": "Заглавие",
    "service_type": "Тип Оферта",
    "property_type": "Тип Имот",
    "location": "Населено Място",
    "district": "Квартал",
    "price": "Цена",
    "currency": "Валута",
    "area": "Квадратура",
    "floor": "Етаж",
    "floor_count": "Общо Етажи",
    "heating": "Отопление",
    "structure": "Строителство",
    "description": "Описание",
    "location_description": "Местоположение",
    "room_distribution": "Разпределение",
    "condition": "Състояние",
    "building_description": "Изложение",
    "additional_information": "Допълнителна информация",
    "agent": "Брокер",
}

########################
# Model Helpers

SERVICE_TYPE = [("Продава", "Продава"), ("Наем", "Наем")]

PROPERTY_TYPE = [
    ("Апартамент", "Апартамент"),
    ("1-стаен", "1-стаен"),
    ("2-стаен", "2-стаен"),
    ("3-стаен", "3-стаен"),
    ("4-стаен", "4-стаен"),
    ("Многостаен", "Многостаен"),
    ("Мезонет", "Мезонет"),
    ("Ателие", "Ателие"),
    ("Таван", "Таван"),
    ("Офис", "Офис"),
    ("Склад", "Склад"),
    ("Промишлено помещение", "Промишлено помещение"),
    ("Магазин", "Магазин"),
    ("Парцел", "Парцел"),
    ("Гараж", "Гараж"),
    ("Къща", "Къща"),
    ("Етаж от къща", "Етаж от къща"),
    ("Заведение", "Заведение"),
    ("Хотел", "Хотел"),
]

DISTRICT = [
    ("Аерогарата", "Аерогарата"),
    ("Банишора", "Банишора"),
    ("Бели Брези", "Бели Брези"),
    ("Бенковски", "Бенковски"),
    ("Борово", "Борово"),
    ("Бояна", "Бояна"),
    ("Бъкстон", "Бъкстон"),
    ("Витоша", "Витоша"),
    ("Военна Рампа", "Военна Рампа"),
    ("Враждебна", "Враждебна"),
    ("Връбница", "Връбница"),
    ("Гара Искър", "Гара Искър"),
    ("Гевгелийски", "Гевгелийски"),
    ("Гео Милев", "Гео Милев"),
    ("Горна Баня", "Горна Баня"),
    ("Горубляне", "Горубляне"),
    ("Гоце Делчев", "Гоце Делчев"),
    ("Дианабад", "Дианабад"),
    ("Драгалевци", "Драгалевци"),
    ("Дружба 1", "Дружба 1"),
    ("Дружба 2", "Дружба 2"),
    ("Дървеница", "Дървеница"),
    ("Западен Парк", "Западен Парк"),
    ("Захарна Фабрика", "Захарна Фабрика"),
    ("Зона Б-4", "Зона Б-4"),
    ("Зона Б18", "Зона Б18"),
    ("Зона Б19", "Зона Б19"),
    ("Зона Б5", "Зона Б5"),
    ("Иван Вазов", "Иван Вазов"),
    ("Изгрев", "Изгрев"),
    ("Изток", "Изток"),
    ("Илинден", "Илинден"),
    ("Илиянци", "Илиянци"),
    ("Княжево", "Княжево"),
    ("Красна Поляна", "Красна Поляна"),
    ("Красно Село", "Красно Село"),
    ("Кремиковци", "Кремиковци"),
    ("Кръстова Вада", "Кръстова Вада"),
    ("Лагера", "Лагера"),
    ("Левски", "Левски"),
    ("Левски В", "Левски В"),
    ("Левски Г", "Левски Г"),
    ("Лозенец", "Лозенец"),
    ("Люлин 1", "Люлин 1"),
    ("Люлин 10", "Люлин 10"),
    ("Люлин 2", "Люлин 2"),
    ("Люлин 3", "Люлин 3"),
    ("Люлин 4", "Люлин 4"),
    ("Люлин 5", "Люлин 5"),
    ("Люлин 6", "Люлин 6"),
    ("Люлин 7", "Люлин 7"),
    ("Люлин 8", "Люлин 8"),
    ("Люлин 9", "Люлин 9"),
    ("Люлин Център", "Люлин Център"),
    ("Малинова Долина", "Малинова Долина"),
    ("Манастирски Ливади", "Манастирски Ливади"),
    ("Медицинска Академия", "Медицинска Академия"),
    ("Младост 1", "Младост 1"),
    ("Младост 1А", "Младост 1А"),
    ("Младост 2", "Младост 2"),
    ("Младост 3", "Младост 3"),
    ("Младост 4", "Младост 4"),
    ("Модерно Предградие", "Модерно Предградие"),
    ("Мотописта", "Мотописта"),
    ("Мусагеница", "Мусагеница"),
    ("Надежда 1", "Надежда 1"),
    ("Надежда 2", "Надежда 2"),
    ("Надежда 3", "Надежда 3"),
    ("Надежда 4", "Надежда 4"),
    ("Надежда 5", "Надежда 5"),
    ("Надежда 6", "Надежда 6"),
    ("Обеля", "Обеля"),
    ("Обеля 1", "Обеля 1"),
    ("Обеля 2", "Обеля 2"),
    ("Оборище", "Оборище"),
    ("Овча Купел", "Овча Купел"),
    ("Овча Купел 1", "Овча Купел 1"),
    ("Овча Купел 2", "Овча Купел 2"),
    ("Орландовци", "Орландовци"),
    ("Павлово", "Павлово"),
    ("Панчарево", "Панчарево"),
    ("Подуяне", "Подуяне"),
    ("Полигона", "Полигона"),
    ("Разсадника", "Разсадника"),
    ("Редута", "Редута"),
    ("Света Троица", "Света Троица"),
    ("Свобода", "Свобода"),
    ("Сердика", "Сердика"),
    ("Симеоново", "Симеоново"),
    ("Славия", "Славия"),
    ("Слатина", "Слатина"),
    ("Стрелбище", "Стрелбище"),
    ("Студентски Град", "Студентски Град"),
    ("Суха Река", "Суха Река"),
    ("Суходол", "Суходол"),
    ("Толстой", "Толстой"),
    ("Требич", "Требич"),
    ("Триъгълника", "Триъгълника"),
    ("Фондови Жилища", "Фондови Жилища"),
    ("Хаджи Димитър", "Хаджи Димитър"),
    ("Хиподрума", "Хиподрума"),
    ("Хладилника", "Хладилника"),
    ("Център", "Център"),
    ("Яворов", "Яворов"),
]

CURRENCIES = [("€", "€"), ("лв.", "лв."), ("$", "$")]

STRUCTURE = [
    ("Тухла", "Тухла"),
    ("Панел", "Панел"),
    ("Ново Строителство", "Ново Строителство"),
]


HEATING = [
    ("Газ", "Газ"),
    ("Централно Отопление", "Централно Отопление"),
]

########################
# Client Specific Data

USLUGI = [
    {
        "id": 1,
        "name": "Покупка",
        "data": [
            "При покупка на недвижим имот агенция Anri Estate осигурява следните услуги:",
            "Anri Estate съветва и подпомага клиента при избора на имот. Проучване на пазара за недвижими имоти към момента по зададени от купувача параметри, за да могат да бъдат напълно удовлетворени неговите изисквания.",
            "Агенцията подготвя пълния набор от документи за собственост на съответния имот и осъществява проверка за наличие на евентуални тежести (ипотеки, възбрани и пр.)",
            "Anri Estate оказва юридическа и консултантска помощ до деня на нотариално прехвърляне и получаване на нотариалния акт за новозакупения имот.",
            "Ние обезпечаваме Вашата заявка за покупка на недвижим имот чрез реклама във водещите печатни и интернет издания в областта на недвижимите имоти. Наши служители провеждат с Вас огледи на предварително подбрани и одобрени от Вас имоти, като Вие сте предварително информирани за качествата на всеки един имот.",
            "Ние Ви даваме пълна информация за моментното състояние на пазара за недвижими имоти, като по този начин Вие можете да коригирате параметрите на вашето търсене, така че да получите най-доброто от пазара.",
            'Можете да подадете оферта за предлагания от Вас имот в нашия офис или <a href="mailto:<EMAIL>" target="_blank" class="underline text-accent">изпратете мейл</a>.',
        ],
    },
    {
        "id": 2,
        "name": "Продажба",
        "data": [
            "При продажба на имот Anri Estate осигурява на клиентите си следния пакет услуги:",
            "Агенцията Anri Estate консултира продавача, за да може да определи реалната пазарна цена на предлагания имот към съответния момент и актуализирането й във времето спрямо пазарната конюнктура.",
            "Агенцията Anri Estate осъществява маркетингова и рекламна стратегия по предлагането на имота с цел максимално бързо да се намерят клиенти за предлагания имот и максимално изгодното му реализиране.",
            "“Anri Estate“ изисква пълен набор документи от продавача, необходими за нотариалното прехвърляне на имота.",
            "Агенция “Anri Estate” защитава интересите на своите клиенти като осигурява необходимата юридическа и консултантска помощ до деня на нотариалното прехвърляне на имота и получаване на пълната стойност на продадения имот.",
            "Ние осигуряваме публичност на Вашата оферта за продажба на недвижим имот чрез реклама във водещите печатни и интернет издания в областта на недвижимите имоти.",
            "Наши служители провеждат огледи на имота с потенциални купувачи, предварително информирани за качествата на Вашия имот.",
            "Ние Ви даваме пълна информация за моментното състояние на пазара за недвижими имоти, като по този начин Вие можете да коригирате параметрите на офертата си, така че тя да бъде конкурентноспособна на този пазар.",
        ],
    },
    {
        "id": 3,
        "name": "Отдаване под наем",
        "data": [
            'При поръчка за отдаване имот под наем "ANRI ESTATE” осигурява:',
            "Проучване на предлаганите под наем недвижими имоти със сходни характеристики към дадения момент.",
            "Оказва помощ при формиране на икономически обоснована оценка на имоти, според пазарната конюнктура.",
            "Екипът на агенцията изготвя индивидуална рекламна стратегия при предлагането на обявения имот, ориентирана към максимално доброто му присъствие на информационни пазар за недвижими имоти и целяща намирането на максимално подходящ наемател.",
            "Изискване на документ за собственост от Наемодателя и други съпътстващи документи, необходими при сключване на договор за наем.",
            "Изготвяне на договор за наем според ЗЗД и взаимната договореност между страните.",
            "Агенцията защитава интересите на Наемодателя и оказва подкрепа през целия наемен период.",
            "Ние осигуряваме публичност на Вашата оферта за отдаване под наем на недвижим имот чрез реклама във водещите печатни и интернет издания в областта на недвижимите имоти. Наши служители провеждат огледи на имота с потенциални наематели, предварително информирани за качествата на Вашия имот.",
            "Ние Ви даваме пълна информация за моментното състояние на пазара за недвижими имоти, като по този начин Вие можете да коригирате параметрите на офертата си, така че тя да бъде конкурентноспособна на този пазар.",
            'Можете да подадете оферта за предлагания от Вас имот в нашия офис или <a href="mailto:<EMAIL>" target="_blank" class="underline text-accent">изпратете мейл</a>.',
        ],
    },
    {
        "id": 4,
        "name": "Наемане на имот",
        "data": [
            "При поръчка за търсене на имот под наем ”Anri Estate” осигурява:",
            "Проучване на предлаганите под наем недвижими имоти със сходни характеристики към дадения момент.",
            "Оказва помощ при формиране на икономически обоснована оценка на имоти, според пазарната конюнктура.",
            "Екипът на агенцията изготвя индивидуална рекламна стратегия при предлагането на обявения имот, ориентирана към максимално доброто му присъствие на информационни пазар за недвижими имоти и целяща намирането на максимално подходящ наемател.",
            "Изискване на документ за собственост от Наемодателя и други съпътстващи документи, необходими при сключване на договор за наем.",
            "Изготвяне на договор за наем според ЗЗД и взаимната договореност между страните.",
            "Агенцията защитава интересите на Наемодателя и оказва подкрепа през целия наемен период.",
            "Ние осигуряваме публичност на Вашата оферта за отдаване под наем на недвижим имот чрез реклама във водещите печатни и интернет издания в областта на недвижимите имоти. Наши служители провеждат огледи на имота с потенциални наематели, предварително информирани за качествата на Вашия имот.",
            "Ние Ви даваме пълна информация за моментното състояние на пазара за недвижими имоти, като по този начин Вие можете да коригирате параметрите на офертата си, така че тя да бъде конкурентноспособна на този пазар.",
        ],
    },
    {
        "id": 5,
        "name": "Управление на имот",
        "data": [
            "При сключен договор за управление на имот ние Ви предлагаме:",
            "Регулярна реклама и ефективни огледи на имота.",
            "Отдаване на имота под наем и юридическо обезпечаване на сделката.",
            "Осигуряването на най-подходящите наематели.",
            "Постоянна заетост на отдавания под наем имот.",
            "Съхраняване на предоставения депозит по договора за наем.",
            "Контролиране на изпълнението на всички клаузи по договора, в това число заплащане в срок на месечния наем и консумативите.",
            "Осъществяване надзор над състоянието на отдадения имот.",
            "Извършване на основните ремонти, необходими в наетия имот, които са задължение на наемодателя.",
            "Координиране на дейности, свързани с проекти за вътрешна пруестройство, иннериорен дизайн и обзавеждане.",
            "Застраховане на имота при изискване на някоя от страните.",
            "Представляване на собственика на имота пред всички институции и организации.",
            "Представляване на собственика пред общото събрание на сградата, в която се намира имота.",
            "Ежемесечен отчет за дейността на агенцията във връзка с имота, при изискване от собственика.",
        ],
    },
    {
        "id": 6,
        "name": "Юридическа подкрепа",
        "data": [
            "В услуга на своите клиенти агенцията предлага и осигурява професионални юридически консултации."
        ],
    },
]
