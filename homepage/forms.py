from django import forms

from .models import (
    Property,
    Agent,
    ServiceType,
    PropertyType,
    Location,
    District,
    Currency,
    Heating,
    Structure,
    ServiceType,
)
from .utils import (
    property_model_fields,
    SERVICE_TYPE,
    PROPERTY_TYPE,
    DISTRICT,
    HEATING,
    STRUCTURE,
)

from .widgets import CustomTextarea

forms.Textarea = CustomTextarea


class CustomErrorMessagesMixin:
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        for field in self.fields.values():
            field.error_messages = {
                "required": "Полето не може да е празно.",
                "unique": "Съществува вече. Моля променете стойността.",
            }


class PropertyForm(CustomErrorMessagesMixin, forms.ModelForm):
    class Meta:
        model = Property
        fields = "__all__"

        help_texts = {}

        labels = {
            "offer_nr": "Оферта №",
            "is_active": "Активна Оферта",
            "top_offer": "Топ Оферта",
            "main_image": "Заглавна Снимка",
            "heading": "Заглавие",
            "service_type": "Тип Оферта",
            "property_type": "Тип Имот",
            "location": "Населено Място",
            "district": "Квартал",
            "price": "Цена",
            "currency": "Валута",
            "area": "Квадратура",
            "floor": "Етаж",
            "floor_count": "Общо Етажи",
            "heating": "Отопление",
            "structure": "Строителство",
            "description": "Описание",
            "location_description": "Местоположение",
            "room_distribution": "Разпределение",
            "condition": "Състояние",
            "building_description": "Изложение",
            "additional_information": "Допълнителна информация",
            "agent": "Брокер",
        }

        widgets = {
            # "top_offer": forms.Select(
            #     choices=[
            #         (None, "---"),
            #         (True, "Да"),
            #         (False, "Не"),
            #     ]
            # ),
            # "location": forms.TextInput(attrs={"value": "София"}),
            # "location_description": forms.Textarea(
            #     attrs={"placeholder": "Напр.: 'Намира се в близост до ул. Нарцис'"},
            # ),
        }


class AgentForm(forms.ModelForm):
    class Meta:
        model = Agent
        fields = "__all__"
        labels = {
            "first_name": "Име",
            "last_name": "Фамилия",
            "phone": "Телефон",
            "image": "Снимка",
        }
        widgets = {
            "phone": forms.TextInput(attrs={"type": "tel"}),
        }


class SearchForm(forms.Form):
    query = forms.CharField(
        max_length=100,
        required=False,
        label="Какво Търсите",
        widget=forms.TextInput(
            attrs={"placeholder": "Какво Търсите"},
        ),
        error_messages={
            "required": "Това поле е задължително.",
            "max_length": "Текста трябва да е по-къс от 100 знака.",
        },
    )

    service_type = forms.ModelChoiceField(
        label="Покупка/Наем",
        queryset=ServiceType.objects.all(),
        required=False,
    )

    property_type = forms.ModelChoiceField(
        label="Вид имот",
        queryset=PropertyType.objects.all(),
        required=False,
    )
    district = forms.ModelChoiceField(
        label="Квартал",
        queryset=District.objects.all(),
        required=False,
    )
    price_from = forms.IntegerField(label="Цена ОТ", required=False)
    price_to = forms.IntegerField(label="Цена ДО", required=False)
    area_from = forms.IntegerField(label="Квадратура ОТ", required=False)
    area_to = forms.IntegerField(label="Квадратура ДО", required=False)

    heating = forms.ModelChoiceField(
        label="Отопление",
        queryset=Heating.objects.all(),
        required=False,
    )
    structure = forms.ModelChoiceField(
        queryset=Structure.objects.all(),
        label="Строителен Материал",
        required=False,
    )

    not_first_floor = forms.BooleanField(label="Без първи", required=False)
    not_last_floor = forms.BooleanField(label="Без последен", required=False)
    floor_from = forms.IntegerField(label="Етаж ОТ", required=False)
    floor_to = forms.IntegerField(label="Етаж ДО", required=False)

    sort = forms.ChoiceField(
        label="Сортирай по...",
        choices=[
            ("", "Сортирай по..."),
            ("price", "\u2197 Цена"),
            ("-price", "\u2198 Цена"),
            ("created", "\u2197 Дата"),
            ("-created", "\u2198 Дата"),
            ("area", "\u2197 Квадратура"),
            ("-area", "\u2198 Квадратура"),
        ],
        required=False,
    )


# Generics
class ServiceTypeForm(forms.ModelForm):
    class Meta:
        model = ServiceType
        fields = ("name",)


class PropertyTypeForm(forms.ModelForm):
    class Meta:
        model = PropertyType
        fields = ("name",)


class LocationForm(forms.ModelForm):
    class Meta:
        model = Location
        fields = ("name",)


class DistrictForm(forms.ModelForm):
    class Meta:
        model = District
        fields = ("name",)


class CurrencyForm(forms.ModelForm):
    class Meta:
        model = Currency
        fields = ("name",)


class HeatingForm(forms.ModelForm):
    class Meta:
        model = Heating
        fields = ("name",)


class StructureForm(forms.ModelForm):
    class Meta:
        model = Structure
        fields = ("name",)
