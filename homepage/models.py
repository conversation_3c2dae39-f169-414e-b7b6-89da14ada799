from django.db import models

import uuid

from .utils import SERVICE_TYPE
from .utils import PROPERTY_TYPE
from .utils import DISTRICT
from .utils import STRUCTURE
from .utils import HEATING
from .utils import CURRENCIES

from .utils import resize_and_watermark, generate_random_string


def create_path_property(instance, filename):
    return f"{instance.offer_nr}/{filename}"


def create_path_photo(instance, filename):
    return f"{instance.property.offer_nr}/{filename}"


def create_path_agent(instance, filename):
    return f"agents/{instance.last_name}/{filename}"


# Create your models here.
class Property(models.Model):
    id = models.UUIDField(
        default=uuid.uuid4, unique=True, primary_key=True, editable=False
    )

    offer_nr = models.IntegerField(unique=True)
    is_active = models.BooleanField(default=True)
    top_offer = models.BooleanField(default=False)
    main_image = models.ImageField(upload_to=create_path_property)
    heading = models.TextField(
        max_length=200,
    )
    # service_type = models.CharField(choices=SERVICE_TYPE, max_length=50)
    service_type = models.ForeignKey(
        "ServiceType",
        on_delete=models.PROTECT,
    )

    # property_type = models.CharField(choices=PROPERTY_TYPE, max_length=50)
    property_type = models.ForeignKey(
        "PropertyType",
        on_delete=models.PROTECT,
    )

    # location = models.CharField(max_length=100, null=True, blank=True)
    location = models.ForeignKey(
        "Location",
        on_delete=models.PROTECT,
    )
    # district = models.CharField(max_length=100, null=True, blank=True)
    district = models.ForeignKey(
        "District",
        on_delete=models.PROTECT,
        blank=True,
        null=True,
    )

    price = models.IntegerField()
    # currency = models.CharField(
    #     choices=CURRENCIES,
    #     default=CURRENCIES[0][0],
    #     max_length=4,
    #     null=True,
    #     blank=True,
    # )
    currency = models.ForeignKey(
        "Currency",
        on_delete=models.PROTECT,
    )
    area = models.IntegerField()
    floor = models.IntegerField(null=True, blank=True)
    floor_count = models.IntegerField(null=True, blank=True)

    # heating = models.CharField(choices=HEATING, max_length=32, null=True, blank=True)
    heating = models.ForeignKey(
        "Heating",
        on_delete=models.PROTECT,
        blank=True,
        null=True,
    )

    # structure = models.CharField(
    #     choices=STRUCTURE, max_length=32, null=True, blank=True
    # )
    structure = models.ForeignKey(
        "Structure",
        on_delete=models.PROTECT,
        blank=True,
        null=True,
    )

    description = models.TextField(blank=True, null=True)
    location_description = models.TextField(null=True, blank=True)
    room_distribution = models.TextField(blank=True, null=True)
    condition = models.TextField(blank=True, null=True)
    building_description = models.TextField(blank=True, null=True)
    additional_information = models.TextField(blank=True, null=True)

    created = models.DateTimeField(auto_now_add=True)

    agent = models.ForeignKey("Agent", on_delete=models.PROTECT, blank=True, null=True)

    class Meta:
        verbose_name_plural = "Properties"

    def __str__(self):
        return f"{self.offer_nr} ({self.is_active}, {self.service_type}) - {self.property_type}, {self.location}, {self.district}"

    def save(self, *args, **kwargs):
        if self.main_image:
            main_image_name = f"{self.offer_nr}_{generate_random_string(7)}.jpg"

            resize_and_watermark(
                self.main_image,
                image_name=main_image_name,
                watermark_text="Anri Estate",
                target_pixel_size=800,
            )
        super(Property, self).save(*args, **kwargs)


class Photo(models.Model):
    property = models.ForeignKey(
        Property, related_name="photos", on_delete=models.CASCADE
    )
    image = models.ImageField(upload_to=create_path_photo, null=True)

    def save(self, *args, **kwargs):
        if self.image:
            image_name = f"{self.property.offer_nr}_{generate_random_string(7)}.jpg"

            resize_and_watermark(
                self.image,
                watermark_text="Anri Estate",
                image_name=image_name,
                target_pixel_size=800,
            )
        super(Photo, self).save(*args, **kwargs)


class Agent(models.Model):
    first_name = models.CharField(max_length=100)
    last_name = models.CharField(max_length=100)
    phone = models.CharField(max_length=100, null=True, blank=True)
    email = models.EmailField(max_length=100, null=True, blank=True)
    image = models.ImageField(upload_to=create_path_agent, null=True, blank=True)

    def __str__(self):
        return f"{self.first_name} {self.last_name}"

    def save(self, *args, **kwargs):
        if self.image:
            image_name = f"{self.last_name}_{generate_random_string(7)}.jpg"

            resize_and_watermark(
                self.image,
                image_name=image_name,
                target_pixel_size=600,
            )
        super(Agent, self).save(*args, **kwargs)


# Generics
class ServiceType(models.Model):
    name = models.CharField(
        unique=True,
        max_length=128,
        null=False,
        blank=False,
        verbose_name="тип оферта",
    )
    gender = models.CharField(
        max_length=10,
        null=True,
        blank=True,
        verbose_name="този",
    )

    def __str__(self):
        return f"{self.name}"


class PropertyType(models.Model):
    name = models.CharField(
        unique=True,
        max_length=128,
        null=False,
        blank=False,
        verbose_name="тип имот",
    )
    gender = models.CharField(
        max_length=10,
        null=True,
        blank=True,
        verbose_name="този",
    )

    def __str__(self):
        return f"{self.name}"


class Location(models.Model):
    name = models.CharField(
        unique=True,
        max_length=100,
        null=False,
        blank=False,
        verbose_name="град",
    )
    gender = models.CharField(
        max_length=10,
        null=True,
        blank=True,
        verbose_name="този",
    )

    def __str__(self):
        return f"{self.name}"


class District(models.Model):
    name = models.CharField(
        unique=True,
        max_length=100,
        null=True,
        blank=True,
        verbose_name="квартал",
    )
    gender = models.CharField(
        max_length=10,
        null=True,
        blank=True,
        verbose_name="този",
    )

    def __str__(self):
        return f"{self.name}"


class Currency(models.Model):
    name = models.CharField(
        unique=True,
        max_length=100,
        null=True,
        blank=True,
        verbose_name="валута",
    )
    gender = models.CharField(
        max_length=10,
        null=True,
        blank=True,
        verbose_name="тази",
    )

    def __str__(self):
        return f"{self.name}"


class Heating(models.Model):
    name = models.CharField(
        unique=True,
        max_length=100,
        null=True,
        blank=True,
        verbose_name="отопление",
    )
    gender = models.CharField(
        max_length=10,
        null=True,
        blank=True,
        verbose_name="това",
    )

    def __str__(self):
        return f"{self.name}"


class Structure(models.Model):
    name = models.CharField(
        unique=True,
        max_length=100,
        null=True,
        blank=True,
        verbose_name="строителство",
    )
    gender = models.CharField(
        max_length=10,
        null=True,
        blank=True,
        verbose_name="това",
    )

    def __str__(self):
        return f"{self.name}"
