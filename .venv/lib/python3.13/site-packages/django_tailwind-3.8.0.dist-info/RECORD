django_tailwind-3.8.0.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
django_tailwind-3.8.0.dist-info/LICENSE,sha256=htQDvjKtOLJxi8Tz0hybW1juf4G24j4iKgXTP1JovzQ,1078
django_tailwind-3.8.0.dist-info/METADATA,sha256=dcjMNu2-dfNqXuCZRMjmqis9Q088xH3n6n0vJUxXJCc,2629
django_tailwind-3.8.0.dist-info/RECORD,,
django_tailwind-3.8.0.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
django_tailwind-3.8.0.dist-info/WHEEL,sha256=Zb28QaM1gQi8f4VCBhsUklF61CTlNYfs9YAZn-TOGFk,88
tailwind/__init__.py,sha256=CRzJlI9k7yXv1Rfx8zUXWAtfAt9oQOZqCJvEt5HSWKk,524
tailwind/__pycache__/__init__.cpython-313.pyc,,
tailwind/__pycache__/apps.cpython-313.pyc,,
tailwind/__pycache__/npm.cpython-313.pyc,,
tailwind/__pycache__/utils.cpython-313.pyc,,
tailwind/__pycache__/validate.cpython-313.pyc,,
tailwind/app_template/cookiecutter.json,sha256=F4wiRyFqW0l9w3B2NvqTKdW-jiRdaR9ok63pn3q7Tho,80
tailwind/app_template/hooks/__pycache__/pre_gen_project.cpython-313.pyc,,
tailwind/app_template/hooks/pre_gen_project.py,sha256=qMM4UNU7ElqLJHI61WmKyXVwYIPdNmegUw8bXkf8qd8,292
tailwind/app_template/{{cookiecutter.app_name}}/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
tailwind/app_template/{{cookiecutter.app_name}}/__pycache__/__init__.cpython-313.pyc,,
tailwind/app_template/{{cookiecutter.app_name}}/apps.py,sha256=jcTyTzc1Yq8Bd2fh7SFt-_YCU7Xh2lRX_PZlZuJU96g,169
tailwind/app_template/{{cookiecutter.app_name}}/static_src/.gitignore,sha256=FtMORGIYn7FN1hG9twjFEGMMV2ofNbk4PomkNS2jbJc,13
tailwind/app_template/{{cookiecutter.app_name}}/static_src/package.json,sha256=ON9GKnVh2nXGJfOdsqqGfz9rmHhgIiyy-tnciGfTYUM,981
tailwind/app_template/{{cookiecutter.app_name}}/static_src/postcss.config.js,sha256=ykvWj3ftwiOvYH45RtyXnDPXiSFn55Y6-Kd62HahDk0,121
tailwind/app_template/{{cookiecutter.app_name}}/static_src/src/styles.css,sha256=zBp60NAZ3bHTLQ7LWIugrCbOQdhiXdbDZjSLJfg6KOw,59
tailwind/app_template/{{cookiecutter.app_name}}/static_src/tailwind.config.js,sha256=5XUxfI0OigO27rd-LH4R0UjOnZ4tJeaD6JSkPUfXsgU,1891
tailwind/app_template/{{cookiecutter.app_name}}/templates/base.html,sha256=a2QubGMnr7ohaFXfq83rBtmsxKbJ40XFzb64jHpiZvQ,556
tailwind/apps.py,sha256=_OUAG1Bnh1CjoNshiCQntp6-zB1i-YDtxvBMwqQeGV0,91
tailwind/management/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
tailwind/management/__pycache__/__init__.cpython-313.pyc,,
tailwind/management/commands/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
tailwind/management/commands/__pycache__/__init__.cpython-313.pyc,,
tailwind/management/commands/__pycache__/tailwind.cpython-313.pyc,,
tailwind/management/commands/tailwind.py,sha256=FuMEl2BhvPfHv4vrtChMJybm2fmpqqp_5_MpyK9dub8,4121
tailwind/npm.py,sha256=lo4_Jzi1-dZH0B4rMZkXwo4qr2kbb450VFXxzSSIbws,1150
tailwind/templates/tailwind/tags/css.html,sha256=vIRXcC9sRxCrUh7HEGn6z0X_6GkpJ8ZxuqNGRw9VBKA,494
tailwind/templates/tailwind/tags/preload_css.html,sha256=I3f9JJHjVGlsCrkVnG5QiGvfTEUmi1miQz3weyXcb8c,187
tailwind/templatetags/__pycache__/tailwind_tags.cpython-313.pyc,,
tailwind/templatetags/tailwind_tags.py,sha256=H0f9g-KHMywhcg8kqxjqws02m9pOXVeJquBdRnvggDE,896
tailwind/utils.py,sha256=kDTEZa8UobFk-Ud45i00p0HJG6wBhc6dLN0zOarQ8-8,762
tailwind/validate.py,sha256=2o-lLDMQAtN9XH28vR7VaFlhC23AfQRZ1YiEc5ZFJFE,1018
