{% extends "homepage/base.html" %}
{% load widget_tweaks %}

{% block content %}
  <div class="mt-20 px-16 flex flex-col justify-center items-center">
    <div class="w-full max-w-lg p-12 border border-slate-200 rounded-md shadow-md -translate-y-6 bg-white">
      <h1 class="text-3xl font-bold pb-8">Вход</h1>
      <form novalidate action="" method="post" class="flex flex-col gap-4">
        {% csrf_token %}

        {% for field in form %}<c-form-input :field=field />{% endfor %}

        {% for error in form.non_field_errors %}<p class="text-red-400">{{ error }}</p>{% endfor %}

        <c-button-default type="submit">
        Вход
        </c-button-default>
      </form>
    </div>
  </div>
{% endblock content %}
