// const propertyFilterButton = document.getElementById("property-filter-button");
// const mobileSearchBtn = document.getElementById("mobile-search-button");
// const homepageSearchButton = document.getElementById("homepage-search-button");
// const mobileSearchMenu = document.getElementById("mobile-search-menu");
// const closeMobileSearchMenuBtn = document.getElementById("close-btn");

// const toggleMobileSearch = () => {
//   mobileSearchMenu.classList.toggle("-translate-y-full");
//   document.body.classList.toggle("overflow-hidden");
// };

// if (propertyFilterButton) {
//   propertyFilterButton.addEventListener("click", toggleMobileSearch);
// }
// if (homepageSearchButton) {
//   homepageSearchButton.addEventListener("click", toggleMobileSearch);
// }
// closeMobileSearchMenuBtn.addEventListener("click", toggleMobileSearch);
