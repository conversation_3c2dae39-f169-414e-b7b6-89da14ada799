// Main Image Upload
const mainImageInput = document.getElementById('main-image');
const mainImageLabel = document.getElementById('main-image-label');
const img = document.getElementById('main-image-preview');

mainImageInput.addEventListener('change', function () {
    const file = this.files[0];
    if (file) {
        const reader = new FileReader();
        reader.onload = (e) => {
            img.src = e.target.result;
            img.style.display = 'block';
        };
        reader.readAsDataURL(file);

        mainImageLabel.textContent = 'Промени Снимка';
    }
});

img.addEventListener('click', function () {
    mainImageInput.click();
})


// File Upload Form
const fileInput = document.getElementById('file-upload');
const fileChosen = document.getElementById('file-chosen');
const imagesContainer = document.getElementById('file-upload-images');
const fileUploadLabel = document.getElementById('file-upload-label');

fileInput.addEventListener('change', function () {
    const files = this.files;
    for (const file of files) {
        const newImg = document.createElement('img');

        const reader = new FileReader();
        reader.onload = (e) => {
            newImg.src = e.target.result;
            newImg.classList = "w-20 h-20 object-cover border border-slate-50";
        }
        reader.readAsDataURL(file);

        imagesContainer.appendChild(newImg);
    }

    const filesNum = files.length;
    if (filesNum > 1) {
        fileChosen.textContent = `Има общо ${filesNum} избрани снимки.`
    }
    else if (filesNum == 1) {
        fileChosen.textContent = 'Има една избрана снимка.'
    }

    // Change Button/Label text
    if (fileUploadLabel) {
        fileUploadLabel.textContent = "Промени Снимки";
    }
});