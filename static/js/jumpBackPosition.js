function saveScrollPosition() {
    const scrollPosition = window.scrollY || document.documentElement.scrollTop;
    localStorage.setItem('scrollPosition', scrollPosition);
}

document.addEventListener("DOMContentLoaded", function () {
    const formElements = document.querySelectorAll('form');
    formElements.forEach(form => {
        form.addEventListener('submit', saveScrollPosition);
    });

    // Restore scroll position after page load
    const savedPosition = localStorage.getItem('scrollPosition');
    if (savedPosition) {
        window.scrollTo(0, parseInt(savedPosition));
        localStorage.removeItem('scrollPosition');  // Clean up
    }
});
