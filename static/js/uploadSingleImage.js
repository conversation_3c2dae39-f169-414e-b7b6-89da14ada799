// Main Image Upload
const ImageInput = document.getElementById('image');
const ImageLabel = document.getElementById('image-label');

ImageInput.addEventListener('change', function () {
    const file = this.files[0];
    if (file) {
        const reader = new FileReader();
        reader.onload = (e) => {
            const img = document.getElementById('image-preview');
            img.src = e.target.result;
        };
        reader.readAsDataURL(file);

        ImageLabel.textContent = 'Промени Снимка';
    }
});