const initApp = () => {
    const hamburgerBtn = document.getElementById("hamburger-button");
    const userIcon = document.getElementById('user-icon');
    const mobileMenu = document.getElementById("mobile-menu");

    const toggleHamburgerMenu = () => {
        mobileMenu.classList.toggle("translate-x-full");
        document.body.classList.toggle("overflow-hidden");
    };

    if (hamburgerBtn) {
        hamburgerBtn.addEventListener("click", toggleHamburgerMenu);
    }
    if (userIcon) {
        userIcon.addEventListener("click", toggleHamburgerMenu);
    }

    mobileMenu.addEventListener("click", toggleHamburgerMenu);

};

document.addEventListener("DOMContentLoaded", initApp);
