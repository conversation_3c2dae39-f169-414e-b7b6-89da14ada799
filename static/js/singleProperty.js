const prev = document.getElementById("prev-btn");
const next = document.getElementById("next-btn");
const list = document.getElementById("item-list");
const imageDigits = document.getElementById("image-digits");

const scrollWidth = document.querySelectorAll(".item-list img")[0].offsetWidth;

prev.addEventListener("click", () => {
    list.scrollLeft -= scrollWidth;
});

next.addEventListener("click", () => {
    list.scrollLeft += scrollWidth;
});

// Get the current visible image
document.addEventListener("DOMContentLoaded", function () {
    const carousel = document.getElementById("item-list");
    const images = carousel.querySelectorAll("img");
    const imagesCount = images.length;

    const observer = new IntersectionObserver(
        (entries) => {
            entries.forEach((entry) => {
                if (entry.isIntersecting) {
                    const index = Array.from(images).indexOf(entry.target);
                    imageDigits.textContent = `${index + 1}/${imagesCount}`;
                }
            });
        },
        { threshold: 0.55 }
    ); // Adjust threshold as needed

    images.forEach((image) => observer.observe(image));
});
